# Walker Arm ROS Package

[![License - apache 2.0](https://img.shields.io/:license-Apache%202.0-yellowgreen.svg)](https://opensource.org/licenses/Apache-2.0)
[![License](https://img.shields.io/badge/License-BSD%203--Clause-blue.svg)](https://opensource.org/licenses/BSD-3-Clause)
[![Forked from HIWIN](https://img.shields.io/badge/Forked%20from-HIWIN%20ROS-blue)](https://github.com/HIWINCorporation/hiwin_ros)

這是一個基於 [HIWIN ROS Package](https://github.com/HIWINCorporation/hiwin_ros) fork 並持續開發的專案。原始專案遵循 [ROS-Industrial](http://wiki.ros.org/Industrial) 指南開發，本專案在此基礎上進行了擴展和優化，主要用於 agv + Hiwin 機械手臂控制。

## 專案概述
本專案是 [HIWIN ROS Package](https://github.com/HIWINCorporation/hiwin_ros) 的分支版本，專注於 agv + Hiwin 機械手臂控制功能開發。專案基於 ROS Noetic 開發，整合了 MoveIt! 功能包，提供完整的機器人運動規劃與控制功能。

## 系統需求
- **作業系統：** Ubuntu 20.04 LTS
- **ROS 版本：** Noetic Noetic
- **其他依賴：**
  - ROS Industrial Packages:
    - `industrial_robot_client`
    - `industrial_robot_simulator`
  - ROS Control packages:
    - `controller_manager`
  - ROS Controller packages:
    - `joint_state_controller`

## 安裝說明
1. **安裝 ROS 套件**
   請按照 [ROS Noetic 安裝指南](https://wiki.ros.org/noetic/Installation) 進行安裝。

2. **設定 ROS 環境**
   ```bash
   source /opt/ros/noetic/setup.bash
   ```

3. **建立 ROS 工作空間**
   ```bash
   mkdir -p $HOME/workspace/src
   ```

4. **克隆專案並編譯**
   ```bash
   # 切換到 workspace
   cd $HOME/workspace

   # 克隆專案
   git clone -b noetic-devel https://github.com/Avery320/walker_arm.git src/

   # 更新並安裝依賴
   rosdep update
   rosdep install --from-paths src/ --ignore-src --rosdistro noetic

   # 編譯工作空間
   catkin_make

   # 啟用工作空間
   source $HOME/workspace/devel/setup.bash
   ```

## 使用說明
### ⚠️ **安全注意事項** ⚠️
*強烈建議在實際硬體上使用前，先在模擬環境中測試您的程式碼。*

使用實體機器人時，請確保：
1. 緊急停止功能正常運作。
3. 速度和加速度限制已正確設定。

### 模擬器使用
在模擬環境中測試機器人：
```bash
roslaunch hiwin_ra610_1476_moveit_config moveit_planning_execution.launch sim:=true
```

### 實體機器人控制
連接並控制實體機器人：
```bash
roslaunch hiwin_driver ra6_bringup.launch ra_type:=ra610_1476 robot_ip:=<機器人IP>
```

## 專案結構
```
walker_arm/
├── hiwin_description/          # 機器人描述文件，包含 URDF 和 STL 模型
├── hiwin_driver/              # 機器人驅動程式，負責與實體機器人通信
├── hiwin_ra610_1476_moveit_config/  # MoveIt 配置，用於運動規劃
├── passthrough_controllers/    # 改進的控制器，提供更靈活的運動控制
└── doc/                       # 文件，包含使用說明和圖片
```

### 主要改動
- **passthrough_controllers**：
  - 改進了原始專案的控制器架構
  - 提供更靈活的運動控制選項
  - 優化了與 Walker 機器人的整合

- **hiwin_ra610_1476_moveit_config**：
  - 增加了 agv 自走車
  - 改進了碰撞檢測配置

## 授權
本專案採用以下授權條款：
- Apache 2.0 License - 詳見 [LICENSE](LICENSE) 文件
- BSD 3-Clause License

原始專案版權歸 HIWIN Corporation 所有。本專案作為原始專案的分支，遵循相同的授權條款。

## 致謝
感謝 [HIWIN Corporation](https://github.com/HIWINCorporation) 提供的原始專案。本專案基於 [HIWIN ROS Package](https://github.com/HIWINCorporation/hiwin_ros) 開發，並遵循其授權條款。
