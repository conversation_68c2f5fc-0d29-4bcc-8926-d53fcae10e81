<?xml version="1.0"?>
<robot xmlns:xacro="http://wiki.ros.org/xacro" name="$(arg name)">
  <!-- robot name parameter -->
  <xacro:arg name="name" default="hiwin" />
  <!-- import main macro -->
  <xacro:include filename="$(find hiwin_description)/urdf/ra_macro.xacro" />
  <!-- possible 'ra_type' values: ra605_710, ra610_1355, ra610_1869 -->
  <!-- the default value should raise an error in case this was called without defining the type -->
  <xacro:arg name="ra_type" default="ra6x" />

  <!-- parameters -->
  <xacro:arg name="tf_prefix" default="" />
  <xacro:arg name="joint_limit_params"
    default="$(find hiwin_description)/config/$(arg ra_type)/joint_limits.yaml" />
  <xacro:arg name="kinematics_params"
    default="$(find hiwin_description)/config/$(arg ra_type)/default_kinematics.yaml" />
  <xacro:arg name="visual_params"
    default="$(find hiwin_description)/config/$(arg ra_type)/visual_parameters.yaml" />

  <!-- create link fixed to the "world" -->
  <link name="world" />

  <!-- Safety Floor -->
  <link name="safety_floor">
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <box size="10 10 0.001" />
      </geometry>
    </collision>
  </link>

  <!-- Fixed joint between world and safety floor -->
  <joint name="world_to_safety_floor" type="fixed">
    <parent link="world" />
    <child link="safety_floor" />
    <origin xyz="0 0 0.02" rpy="0 0 0" />
  </joint>

  <!-- AGV Platform -->
  <link name="agv">
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://hiwin_description/meshes/agv/visual/agv.stl" scale="0.001 0.001 0.001"/>
      </geometry>
      <material name="Grey">
        <color rgba="0.5 0.5 0.5 1.0" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://hiwin_description/meshes/agv/collision/agv.stl" scale="0.001 0.001 0.001"/>
      </geometry>
    </collision>
  </link>

  <!-- Fixed joint between world and agv -->
  <joint name="world_to_agv" type="fixed">
    <parent link="world" />
    <child link="agv" />
    <origin xyz="0 0 0" rpy="0 0 0" />
  </joint>

  <!-- arm -->
  <xacro:hiwin_arm name="$(arg name)" tf_prefix="$(arg tf_prefix)" parent="agv"
    joint_limits_parameters_file="$(arg joint_limit_params)"
    kinematics_parameters_file="$(arg kinematics_params)"
    visual_parameters_file="$(arg visual_params)">
    <origin xyz="0 0 0.325" rpy="0 0 0" /> <!-- position robot at 32.5cm height from world -->
  </xacro:hiwin_arm>

  <!-- Realsense 相機 link -->
  <link name="realsense_link">
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.12 0.04 0.04"/>
      </geometry>
      <material name="Blue">
        <color rgba="0.0 0.3 0.8 1.0"/>
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.12 0.04 0.04"/>
      </geometry>
    </collision>
  </link>

  <!-- 固定關節：tool0 -> realsense_link -->
  <joint name="tool0_realsense_joint" type="fixed">
    <parent link="tool0"/>
    <child link="realsense_link"/>
    <!-- 相機位置和方向設定：位於 tool0 y 軸正向 20cm，並設定為光學座標系方向 -->
    <origin xyz="0 0.10 0.10" rpy="${-pi} 0 0"/>
    <!-- <origin xyz="0 0.10 0.10" rpy="${-pi/2} 0 0"/> -->
  </joint>

</robot>