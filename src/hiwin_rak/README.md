# Hiwin Robot Arm Kinematics (hiwin_rak)

這是一個基於 ROS 的機械臂運動學控制專案，提供了多種運動控制方式和路徑規劃功能。

## temp
### IO 執行
```shell
rosrun hiwin_rak io_ctrl.py [DO編號] [on|off]
```


## 專案概述

本專案實現了機械臂的兩種主要運動控制模式：
1. MoveJ (關節運動)
2. MoveL (空間運動)

## 功能特點

### 運動控制模式
- MoveJ (關節運動)
  - 單點控制
  - 多點路徑規劃
- MoveL (空間運動)
  - 單點控制
  - 多點路徑規劃
  - 連續直線運動

### 輸入方式
- XML 文件配置
- 文本文件配置
- 互動式控制

### 控制特性
- 即時停止功能
- 路徑可視化
- 狀態監控
- 錯誤處理

## 文件說明

- `cartesian_path_demo.py`: 笛卡爾路徑規劃示例
- `moveJ_singlept_controller.py`: 關節空間單點控制
- `moveJ_mutipts_controller.py`: 關節空間多點控制
- `moveL_mutipts_controller.py`: 笛卡爾空間多點控制
- `moveL_inputpt_controller.py`: 基礎笛卡爾空間控制器
- `moveL_inputpt_controller_button.py`: 互動式笛卡爾空間控制器
- `moveL_input_txt_controller_button.py`: 文本文件輸入的互動式控制器

## 安裝說明

### 系統要求
- Ubuntu 20.04 或更高版本
- ROS Noetic 或更高版本
- Python 3.8 或更高版本

### 依賴項安裝

1. 安裝 ROS 依賴：
```bash
cd setup
chmod +x install_python_deps.sh
chmod +x install_ros_deps.sh
./install_python_deps.sh
./install_ros_deps.sh
```

## 使用說明

1. 確保已安裝 ROS 環境
2. 將專案放入 ROS 工作空間
3. 編譯工作空間
4. 運行相應的控制器節點

## 依賴項

### ROS 套件
- moveit-commander
- tf
- visualization_msgs
- geometry_msgs
- std_msgs

### Python 套件
- rospy
- numpy
- 其他標準庫套件

## 開發環境

- 作業系統：Linux
- ROS 版本：ROS Noetic
