#!/usr/bin/env python3
import rospy
from visualization_msgs.msg import Mark<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from geometry_msgs.msg import Pose, Point, Quaternion
from std_msgs.msg import String
import moveit_commander
import copy
import math
import tf.transformations as tf_trans
import sys
import os

# 添加 src 目錄到 Python 路徑
sys.path.append(os.path.join(os.path.dirname(__file__), '../src'))
from utils.path_visualizer import visualize_path_points
from utils.cleanup_utils import cleanup_before_execution
from utils.euler_to_quaternion import euler_to_quaternion
from utils.point_loader import load_points_from_file, save_points_to_file

def move_arm_through_points(points, arm_move_group, status_pub, marker_pub):
    try:
        # 設置末端執行器和規劃參數
        arm_move_group.set_end_effector_link("tool0")
        
        # 設置規劃參數
        arm_move_group.set_max_velocity_scaling_factor(1.0)  # 降低速度
        arm_move_group.set_max_acceleration_scaling_factor(0.1)  # 降低加速度
        arm_move_group.set_planning_time(5.0)  # 增加規劃時間
        arm_move_group.set_num_planning_attempts(10)  # 增加規劃嘗試次數
        
        # 創建路徑點列表
        waypoints = []
        for point in points:
            pose = Pose()
            pose.position.x = point["x"]
            pose.position.y = point["y"]
            pose.position.z = point["z"]
            
            # 設置姿態
            pose.orientation = euler_to_quaternion(point["roll"], point["pitch"], point["yaw"])
            waypoints.append(copy.deepcopy(pose))
        
        # 使用 MoveJ 模式（關節空間運動）
        for i, waypoint in enumerate(waypoints):
            status_pub.publish(f"規劃第 {i+1} 個點位...")
            
            # 設置目標姿態
            arm_move_group.set_pose_target(waypoint)
            
            # 規劃並執行
            plan = arm_move_group.plan()
            
            if plan[0]:
                status_pub.publish(f"第 {i+1} 個點位規劃成功，開始執行...")
                success = arm_move_group.execute(plan[1], wait=True)
                if success:
                    status_pub.publish(f"已到達第 {i+1} 個點位")
                    rospy.loginfo(f"已到達第 {i+1} 個點位")
                else:
                    status_pub.publish(f"執行第 {i+1} 個點位失敗")
                    rospy.logwarn(f"執行第 {i+1} 個點位失敗")
            else:
                status_pub.publish(f"第 {i+1} 個點位規劃失敗")
                rospy.logwarn(f"第 {i+1} 個點位規劃失敗")
            
            # 等待一小段時間確保運動完成
            rospy.sleep(0.5)
            
    except Exception as e:
        rospy.logerr("執行過程中發生錯誤: %s", str(e))
        status_pub.publish("執行過程中發生錯誤: " + str(e))
    finally:
        arm_move_group.stop()
        arm_move_group.clear_pose_targets()

def robot_control_node():
    try:
        rospy.init_node('robot_control_node')
        marker_pub = rospy.Publisher('/visualization_marker_array', MarkerArray, queue_size=10)
        status_pub = rospy.Publisher('/arm_status', String, queue_size=10)
        
        # 初始化MoveIt
        moveit_commander.roscpp_initialize([])
        arm_move_group = moveit_commander.MoveGroupCommander("manipulator")
        
        # 等待MoveIt服務啟動
        rospy.sleep(2)
        
        # 執行前的清理
        cleanup_before_execution(arm_move_group, marker_pub, status_pub)
        rospy.sleep(1)  # 等待清理完成
        
        # 從檔案讀取點位數據
        points_file = os.path.join(os.path.dirname(__file__), '../config/points.yaml')
        try:
            target_points = load_points_from_file(points_file)
        except FileNotFoundError:
            # 如果檔案不存在，使用預設點位
            target_points = [
                {"x": -0.6, "y": -0.3, "z": 0.5, "roll": 0.0, "pitch": 0.0, "yaw": 90.0},  # 起點
                {"x": -0.6, "y": 0.1, "z": 0.7, "roll": 0.0, "pitch": 0.0, "yaw": 90.0},   # 第二點
                {"x": -1.0, "y": 0.0, "z": 0.8, "roll": 0.0, "pitch": 0.0, "yaw": 90.0},   # 第三點
            ]
            # 儲存預設點位到檔案
            os.makedirs(os.path.dirname(points_file), exist_ok=True)
            save_points_to_file(target_points, points_file)
        
        # 可視化路徑點
        visualize_path_points(target_points, marker_pub)
        rospy.sleep(2)
        
        # 執行路徑
        move_arm_through_points(target_points, arm_move_group, status_pub, marker_pub)
        rospy.sleep(1)
        
    except Exception as e:
        rospy.logerr("節點執行過程中發生錯誤: %s", str(e))
    finally:
        moveit_commander.roscpp_shutdown()

if __name__ == '__main__':
    try:
        robot_control_node()
    except rospy.ROSInterruptException:
        pass