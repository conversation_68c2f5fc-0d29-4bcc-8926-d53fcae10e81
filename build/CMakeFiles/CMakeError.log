Performing C SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: /home/<USER>/workspace/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_afb83/fast && /usr/bin/make -f CMakeFiles/cmTC_afb83.dir/build.make CMakeFiles/cmTC_afb83.dir/build
make[1]: Entering directory '/home/<USER>/workspace/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_afb83.dir/src.c.o
/usr/bin/cc   -DCMAKE_HAVE_LIBC_PTHREAD   -o CMakeFiles/cmTC_afb83.dir/src.c.o   -c /home/<USER>/workspace/build/CMakeFiles/CMakeTmp/src.c
Linking C executable cmTC_afb83
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_afb83.dir/link.txt --verbose=1
/usr/bin/cc  -DCMAKE_HAVE_LIBC_PTHREAD    -rdynamic CMakeFiles/cmTC_afb83.dir/src.c.o  -o cmTC_afb83 
/usr/bin/ld: CMakeFiles/cmTC_afb83.dir/src.c.o: in function `main':
src.c:(.text+0x48): undefined reference to `pthread_create'
/usr/bin/ld: src.c:(.text+0x50): undefined reference to `pthread_detach'
/usr/bin/ld: src.c:(.text+0x5c): undefined reference to `pthread_join'
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_afb83.dir/build.make:87: cmTC_afb83] Error 1
make[1]: Leaving directory '/home/<USER>/workspace/build/CMakeFiles/CMakeTmp'
make: *** [Makefile:121: cmTC_afb83/fast] Error 2


Source file was:
#include <pthread.h>

void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /home/<USER>/workspace/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_da9ce/fast && /usr/bin/make -f CMakeFiles/cmTC_da9ce.dir/build.make CMakeFiles/cmTC_da9ce.dir/build
make[1]: Entering directory '/home/<USER>/workspace/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_da9ce.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_create   -o CMakeFiles/cmTC_da9ce.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.16/Modules/CheckFunctionExists.c
Linking C executable cmTC_da9ce
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_da9ce.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_create    -rdynamic CMakeFiles/cmTC_da9ce.dir/CheckFunctionExists.c.o  -o cmTC_da9ce  -lpthreads 
/usr/bin/ld: cannot find -lpthreads
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_da9ce.dir/build.make:87: cmTC_da9ce] Error 1
make[1]: Leaving directory '/home/<USER>/workspace/build/CMakeFiles/CMakeTmp'
make: *** [Makefile:121: cmTC_da9ce/fast] Error 2



