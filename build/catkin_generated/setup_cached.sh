#!/usr/bin/env sh
# generated from catkin/python/catkin/environment_cache.py

# based on a snapshot of the environment before and after calling the setup script
# it emulates the modifications of the setup script without recurring computations

# new environment variables

# modified environment variables
export LD_LIBRARY_PATH='/home/<USER>/workspace/devel/lib:/opt/ros/noetic/lib:/opt/ros/noetic/lib/aarch64-linux-gnu'
export PKG_CONFIG_PATH='/home/<USER>/workspace/devel/lib/pkgconfig:/opt/ros/noetic/lib/pkgconfig:/opt/ros/noetic/lib/aarch64-linux-gnu/pkgconfig'
export PWD='/home/<USER>/workspace/build'
export PYTHONPATH="/home/<USER>/workspace/devel/lib/python3/dist-packages:$PYTHONPATH"